[workspace]
resolver = "2"
members = [
    "livekit",
    "livekit-api",
    "livekit-protocol",
    "livekit-ffi",
    "livekit-runtime",
    "libwebrtc",
    "soxr-sys",
    "yuv-sys",
    "imgproc",
    "webrtc-sys",
    "webrtc-sys/build",
]

[workspace.dependencies]
imgproc = { version = "0.3.12", path = "imgproc" }
yuv-sys = { version = "0.3.7", path = "yuv-sys" }
libwebrtc = { version = "0.3.12", path = "libwebrtc" }
livekit-api = { version = "0.4.4", path = "livekit-api" }
livekit-ffi = { version = "0.12.28", path = "livekit-ffi" }
livekit-protocol = { version = "0.4.0", path = "livekit-protocol" }
livekit-runtime = { version = "0.4.0", path = "livekit-runtime" }
livekit = { version = "0.7.14", path = "livekit" }
soxr-sys = { version = "0.1.0", path = "soxr-sys" }
webrtc-sys-build = { version = "0.3.7", path = "webrtc-sys/build" }
webrtc-sys = { version = "0.3.9", path = "webrtc-sys" }
